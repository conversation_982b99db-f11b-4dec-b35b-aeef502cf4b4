from _typeshed import SupportsKeysAndGetItem
from collections.abc import Iterator, MutableMapping
from http.cookiejar import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>iePolicy
from http.cookies import Morse<PERSON>
from typing import Any

class MockRequest:
    type: Any
    def __init__(self, request) -> None: ...
    def get_type(self): ...
    def get_host(self): ...
    def get_origin_req_host(self): ...
    def get_full_url(self): ...
    def is_unverifiable(self): ...
    def has_header(self, name): ...
    def get_header(self, name, default=None): ...
    def add_header(self, key, val): ...
    def add_unredirected_header(self, name, value): ...
    def get_new_headers(self): ...
    @property
    def unverifiable(self): ...
    @property
    def origin_req_host(self): ...
    @property
    def host(self): ...

class MockResponse:
    def __init__(self, headers) -> None: ...
    def info(self): ...
    def getheaders(self, name): ...

def extract_cookies_to_jar(jar, request, response): ...
def get_cookie_header(jar, request): ...
def remove_cookie_by_name(cookiejar, name, domain=None, path=None): ...

class CookieConflictError(RuntimeError): ...

class RequestsCookieJar(CookieJar, MutableMapping[str, str]):  # type: ignore[misc] # conflicting __iter__ in the base classes
    def get(self, name: str, default: str | None = None, domain: str | None = None, path: str | None = None) -> str | None: ...  # type: ignore[override]
    def set(self, name: str, value: str | Morsel[dict[str, str]], **kwargs) -> Cookie | None: ...
    def iterkeys(self) -> Iterator[str]: ...
    def keys(self) -> list[str]: ...  # type: ignore[override]
    def itervalues(self) -> Iterator[str]: ...
    def values(self) -> list[str]: ...  # type: ignore[override]
    def iteritems(self) -> Iterator[tuple[str, str]]: ...
    def items(self) -> list[tuple[str, str]]: ...  # type: ignore[override]
    def list_domains(self) -> list[str]: ...
    def list_paths(self) -> list[str]: ...
    def multiple_domains(self) -> bool: ...
    def get_dict(self, domain: str | None = None, path: str | None = None) -> dict[str, str]: ...
    def __getitem__(self, name: str) -> str: ...
    def __setitem__(self, name: str, value: str | Morsel[dict[str, str]]) -> None: ...
    def __delitem__(self, name: str) -> None: ...
    def set_cookie(self, cookie: Cookie, *args, **kwargs): ...
    def update(self, other: CookieJar | SupportsKeysAndGetItem[str, str]): ...  # type: ignore[override]
    def copy(self) -> RequestsCookieJar: ...
    def get_policy(self) -> CookiePolicy: ...

def create_cookie(name, value, **kwargs): ...
def morsel_to_cookie(morsel): ...
def cookiejar_from_dict(cookie_dict, cookiejar=None, overwrite=True): ...
def merge_cookies(cookiejar, cookies): ...
